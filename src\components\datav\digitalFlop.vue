<template>
  <div id="digital-flop">
    <!-- 教师情况模块 -->
    <div class="digital-flop-item teacher-module">
      <div class="card-header">
        <div class="card-icon">
          <i class="fas fa-user-tie"></i>
        </div>
        <div class="card-title">教师情况</div>
      </div>

      <div class="card-content">
        <!-- 主数据 -->
        <div class="main-data-row">
          <span class="main-label">教师人数</span>
          <div class="main-value">
            <span class="main-number">{{ teacherData.mainNumber.number[0] }}</span>
            <span class="main-unit">人</span>
          </div>
        </div>

        <!-- 子数据 -->
        <div class="sub-data-list">
          <div class="sub-data-row">
            <span class="sub-label">请假</span>
            <div class="sub-value">
              <span class="sub-number">{{ teacherData.leaveNumber.number[0] }}</span>
              <span class="sub-unit">人</span>
            </div>
          </div>
          <div class="sub-data-row">
            <span class="sub-label">公差</span>
            <div class="sub-value">
              <span class="sub-number">{{ teacherData.businessTripNumber.number[0] }}</span>
              <span class="sub-unit">人</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学生情况模块 -->
    <div class="digital-flop-item student-module">
      <div class="card-header">
        <div class="card-icon">
          <i class="fas fa-user-graduate"></i>
        </div>
        <div class="card-title">学生情况</div>
      </div>

      <div class="card-content">
        <!-- 双主数据 -->
        <div class="dual-main-data">
          <div class="dual-main-item">
            <div class="dual-main-number">
              <span class="dual-number">{{ studentData.shouldBeInSchool.number[0] }}</span>
            </div>
            <div class="dual-main-label">应在校学生</div>
          </div>
          <div class="dual-main-item">
            <div class="dual-main-number">
              <span class="dual-number">{{ studentData.actuallyInSchool.number[0] }}</span>
            </div>
            <div class="dual-main-label">实际在校</div>
          </div>
        </div>

        <!-- 子数据 -->
        <div class="sub-data-list">
          <div class="sub-data-row">
            <span class="sub-label">学生出校人数</span>
            <div class="sub-value">
              <span class="sub-number">{{ studentData.outOfSchool.number[0] }}</span>
              <span class="sub-unit">人</span>
            </div>
          </div>
          <div class="sub-data-row">
            <span class="sub-label">校外人数</span>
            <div class="sub-value">
              <span class="sub-number">{{ studentData.outsideSchool.number[0] }}</span>
              <span class="sub-unit">人</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 今日进出门次模块 -->
    <div class="digital-flop-item access-module">
      <div class="card-header">
        <div class="card-icon">
          <i class="fas fa-door-open"></i>
        </div>
        <div class="card-title">今日进出门次</div>
      </div>

      <div class="card-content">
        <!-- 双主数据 -->
        <div class="dual-main-data">
          <div class="dual-main-item">
            <div class="dual-main-number">
              <span class="dual-number blue">{{ accessData.entranceCount.number[0] }}</span>
            </div>
            <div class="dual-main-label">进门次</div>
          </div>
          <div class="dual-main-item">
            <div class="dual-main-number">
              <span class="dual-number blue">{{ accessData.exitCount.number[0] }}</span>
            </div>
            <div class="dual-main-label">出门次</div>
          </div>
        </div>

        <!-- 子数据 -->
        <div class="sub-data-list">
          <div class="sub-data-row">
            <span class="sub-label">高峰时段</span>
            <div class="sub-value">
              <span class="static-value">{{ accessData.peakTime }}</span>
            </div>
          </div>
          <div class="sub-data-row">
            <span class="sub-label">当前在校</span>
            <div class="sub-value">
              <span class="sub-number">{{ accessData.currentInSchool.number[0] }}</span>
              <span class="sub-unit">人</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知公告模块 -->
    <div class="digital-flop-item notice-module">
      <div class="card-header">
        <div class="card-icon">
          <i class="fas fa-bell"></i>
        </div>
        <div class="card-title">通知公告</div>
      </div>

      <div class="card-content">
        <div class="notice-list">
          <div class="notice-item" v-for="notice in noticeData.notices" :key="notice.title">
            <div class="notice-title">{{ notice.title }}</div>
            <div class="notice-date">{{ notice.date }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalFlop',
  data () {
    return {
      // 教师数据
      teacherData: {
        mainNumber: {},
        leaveNumber: {},
        businessTripNumber: {},
        attendanceRate: 96
      },
      // 学生数据
      studentData: {
        shouldBeInSchool: {},
        actuallyInSchool: {},
        outOfSchool: {},
        outsideSchool: {},
        attendanceRate: 96.3
      },
      // 进出门数据
      accessData: {
        entranceCount: {},
        exitCount: {},
        peakTime: '16:00-17:00',
        currentInSchool: {}
      },
      // 通知公告数据
      noticeData: {
        notices: [
          { title: '欢迎新学期！', date: '2024-08-12' },
          { title: '门禁系统升级通知', date: '2024-08-10' },
          { title: '安全管理规定', date: '2024-08-08' },
          { title: '校园网络维护通知', date: '2024-08-06' },
          { title: '图书馆开放时间调整', date: '2024-08-05' },
          { title: '学生宿舍管理规定', date: '2024-08-03' },
          { title: '食堂就餐时间安排', date: '2024-08-01' }
        ]
      },
      // 样式配置 - 统一管理所有样式，采用科技蓝色主题
      styleConfig: {
        mainNumber: {
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#ffffff',
            fontWeight: 'bold',
            fontSize: '48px'
          }
        },
        dualMainNumber: {
          content: '{nt}',
          textAlign: 'center',
          style: {
            fill: '#ffffff',
            fontWeight: 'bold',
            fontSize: '36px'
          }
        },
        dualMainNumberBlue: {
          content: '{nt}',
          textAlign: 'center',
          style: {
            fill: '#03d3ec',
            fontWeight: 'bold',
            fontSize: '36px'
          }
        },
        subNumber: {
          content: '{nt}',
          textAlign: 'right',
          style: {
            fill: '#b8c5d1',
            fontWeight: 'normal',
            fontSize: '20px'
          }
        }
      }
    }
  },
  methods: {
    // 创建数字翻牌配置对象
    createNumberConfig (value, styleType) {
      return {
        number: [value],
        ...this.styleConfig[styleType]
      }
    },

    // 初始化所有模块数据
    initData () {
      // 初始化教师数据
      this.teacherData.mainNumber = this.createNumberConfig(715, 'mainNumber')
      this.teacherData.leaveNumber = this.createNumberConfig(10, 'subNumber')
      this.teacherData.businessTripNumber = this.createNumberConfig(11, 'subNumber')

      // 初始化学生数据
      this.studentData.shouldBeInSchool = this.createNumberConfig(4450, 'dualMainNumber')
      this.studentData.actuallyInSchool = this.createNumberConfig(4285, 'dualMainNumber')
      this.studentData.outOfSchool = this.createNumberConfig(165, 'subNumber')
      this.studentData.outsideSchool = this.createNumberConfig(28, 'subNumber')

      // 初始化进出门数据
      this.accessData.entranceCount = this.createNumberConfig(1245, 'dualMainNumberBlue')
      this.accessData.exitCount = this.createNumberConfig(1180, 'dualMainNumberBlue')
      this.accessData.currentInSchool = this.createNumberConfig(4285, 'subNumber')
    },

    // 更新教师数据
    updateTeacherData () {
      this.teacherData.mainNumber.number = [Math.floor(Math.random() * 100) + 700]
      this.teacherData.leaveNumber.number = [Math.floor(Math.random() * 20) + 5]
      this.teacherData.businessTripNumber.number = [Math.floor(Math.random() * 20) + 5]
      this.teacherData.attendanceRate = Math.floor(Math.random() * 5) + 95
    },

    // 更新学生数据
    updateStudentData () {
      this.studentData.shouldBeInSchool.number = [Math.floor(Math.random() * 100) + 4400]
      this.studentData.actuallyInSchool.number = [Math.floor(Math.random() * 100) + 4200]
      this.studentData.outOfSchool.number = [Math.floor(Math.random() * 50) + 150]
      this.studentData.outsideSchool.number = [Math.floor(Math.random() * 20) + 20]
      this.studentData.attendanceRate = Math.floor(Math.random() * 5) + 95
    },

    // 更新进出门数据
    updateAccessData () {
      this.accessData.entranceCount.number = [Math.floor(Math.random() * 200) + 1200]
      this.accessData.exitCount.number = [Math.floor(Math.random() * 200) + 1100]
      this.accessData.currentInSchool.number = [Math.floor(Math.random() * 100) + 4200]
    },

    // 更新所有数据
    updateAllData () {
      this.updateTeacherData()
      this.updateStudentData()
      this.updateAccessData()
    }
  },
  mounted () {
    // 初始化数据
    this.initData()

    // 每30秒更新一次数据
    setInterval(this.updateAllData, 30000)
  }
}
</script>

<style lang="less">
#digital-flop {
  position: relative;
  flex: 1;
  min-height: 140px;
  max-height: 180px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 15px;
  padding: 15px;
  background: transparent;

  .digital-flop-item {
    flex: 1;
    background: linear-gradient(135deg, rgba(6, 30, 93, 0.8), rgba(15, 45, 120, 0.6));
    border: 1px solid rgba(1, 153, 209, .3);
    border-radius: 12px;
    padding: 18px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(1, 153, 209, 0.2);
    min-height: 140px;
    max-height: 160px;
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .card-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #03d3ec, #1294fb);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      box-shadow: 0 2px 8px rgba(3, 211, 236, 0.3);

      i {
        font-size: 16px;
        color: #fff;
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .main-data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;

    .main-label {
      font-size: 16px;
      color: #b8c5d1;
      font-weight: normal;
    }

    .main-value {
      display: flex;
      align-items: baseline;

      .main-number {
        font-size: 28px;
        color: #ffffff;
        font-weight: bold;
        line-height: 1;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }

      .main-unit {
        font-size: 24px;
        color: #ffffff;
        font-weight: bold;
        margin-left: 8px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .dual-main-data {
    display: flex;
    align-items: center;
    // justify-content: space-around;
    // margin-bottom: 25px;
    // padding-bottom: 15px;

    .dual-main-item {
      text-align: center;
      flex: 1;

      .dual-main-number {

        .dual-number {
          font-size: 28px;
          color: #ffffff;
          font-weight: bold;
          line-height: 1;
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

          &.blue {
            color: #03d3ec;
            text-shadow: 0 1px 3px rgba(3, 211, 236, 0.5);
          }
        }
      }

      .dual-main-label {
        font-size: 14px;
        color: #b8c5d1;
        font-weight: normal;
      }
    }
  }

  .sub-data-list {
    margin-bottom: 20px;

    .sub-data-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .sub-label {
        font-size: 14px;
        color: #b8c5d1;
        font-weight: normal;
      }

      .sub-value {
        display: flex;
        align-items: baseline;

        .sub-unit {
          font-size: 14px;
          color: #b8c5d1;
          margin-left: 4px;
        }

        .static-value {
          font-size: 14px;
          color: #03d3ec;
          font-weight: normal;
        }
      }
    }
  }

  .progress-section {
    margin-top: auto;

    .progress-bar {
      height: 8px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #03d3ec, #1294fb);
        border-radius: 4px;
        transition: width 0.3s ease;
        box-shadow: 0 0 8px rgba(3, 211, 236, 0.5);
      }
    }

    .progress-text {
      font-size: 12px;
      color: #b8c5d1;
      text-align: left;
    }
  }

  .notice-list {
    max-height: 120px;
    overflow-y: auto;
    padding-right: 5px;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #03d3ec, #1294fb);
      border-radius: 2px;

      &:hover {
        background: linear-gradient(135deg, #1294fb, #03d3ec);
      }
    }

    .notice-item {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(1, 153, 209, 0.2);
      border-radius: 6px;
      padding: 8px 12px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(1, 153, 209, 0.4);
        transform: translateY(-1px);
      }

      .notice-title {
        font-size: 13px;
        color: #ffffff;
        margin-bottom: 3px;
        line-height: 1.3;
        font-weight: 500;
      }

      .notice-date {
        font-size: 11px;
        color: #b8c5d1;
      }
    }
  }
}
</style>
