<template>
  <div id="water-level-chart">
    <!-- 标题区域 -->
    <div class="card-header">
      <div class="card-icon">
        <i class="fas fa-users"></i>
      </div>
      <div class="card-title">访客管理</div>
    </div>

    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content">
      <!-- 左侧：访客概况和类型分布 -->
      <div class="left-panel">
        <!-- 访客概况 -->
        <div class="visitor-overview">
          <div class="overview-card">
            <div class="card-number">23</div>
            <div class="card-label">预约来访</div>
          </div>
          <div class="overview-card">
            <div class="card-number">18</div>
            <div class="card-label">在校访客</div>
          </div>
          <div class="overview-card">
            <div class="card-number">8</div>
            <div class="card-label">高开学院</div>
          </div>
          <div class="overview-card">
            <div class="card-number">156</div>
            <div class="card-label">累计访问</div>
          </div>
        </div>

        <!-- 访客类型分布 -->
        <div class="visitor-distribution">
          <h3 class="section-title">访客类型分布</h3>
          <div class="distribution-list">
            <div class="distribution-item">
              <span class="type-label">商务访问</span>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 45%"></div>
              </div>
              <span class="percentage">45%</span>
            </div>
            <div class="distribution-item">
              <span class="type-label">家长来访</span>
              <div class="progress-bar">
                <div class="progress-fill" style="width: 30%"></div>
              </div>
              <span class="percentage">30%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：访客记录表格 -->
      <div class="right-panel">
        <div class="visitor-records">
          <div class="tabs">
            <div class="tab" :class="{ active: activeTab === 'enter' }" @click="switchTab('enter')">进校人员</div>
            <div class="tab" :class="{ active: activeTab === 'exit' }" @click="switchTab('exit')">离校人员</div>
          </div>

          <div class="visitor-table">
            <dv-scroll-board :config="config" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VisitorManagement',
  data () {
    return {
      activeTab: 'enter', // 当前激活的标签页
      // 进校人员数据
      enterVisitorData: {
        header: ['姓名', '访问时间', '接待部门'],
        data: [
          ['张先生', '2025-5-4 14:30', '信息中心'],
          ['李女士', '2025-3-4 13:45', '学工部'],
          ['王总', '2025-1-4 12:20', '校办'],
          ['刘老师', '2025-1-4 11:15', '教务处'],
          ['陈经理', '2025-1-4 10:30', '后勤处'],
          ['赵主任', '2025-1-4 09:45', '财务处'],
          ['孙教授', '2025-1-4 09:20', '科研处'],
          ['周副院长', '2025-1-4 08:55', '教务处']
        ]
      },
      // 离校人员数据
      exitVisitorData: {
        header: ['姓名', '离开时间', '接待部门'],
        data: [
          ['赵女士', '2025-1-4 16:20', '财务处'],
          ['孙先生', '2025-1-4 15:45', '人事处'],
          ['周总', '2025-1-4 15:10', '校办'],
          ['吴老师', '2025-1-4 14:55', '教务处'],
          ['郑经理', '2025-1-4 14:20', '后勤处'],
          ['钱主管', '2025-1-4 13:50', '学工部'],
          ['林处长', '2025-1-4 13:25', '后勤处'],
          ['何副主任', '2025-1-4 12:40', '信息中心']
        ]
      }
    }
  },
  computed: {
    config () {
      const baseConfig = {
        index: false, // 不显示序号
        columnWidth: [100, 150, 120], // 设置列宽
        align: ['center', 'center', 'center'], // 对齐方式
        rowNum: 5, // 显示行数
        headerBGC: '#1981f6', // 表头背景色
        headerHeight: 40, // 表头高度
        oddRowBGC: 'rgba(0, 44, 81, 0.8)', // 奇数行背景色
        evenRowBGC: 'rgba(10, 29, 50, 0.8)', // 偶数行背景色
        waitTime: 2000, // 滚动等待时间（毫秒）
        carousel: 'single', // 滚动方式：single（单行）
        hoverPause: true // 鼠标悬停时暂停滚动
      }

      if (this.activeTab === 'enter') {
        return {
          ...baseConfig,
          header: this.enterVisitorData.header,
          data: this.enterVisitorData.data
        }
      } else {
        return {
          ...baseConfig,
          header: this.exitVisitorData.header,
          data: this.exitVisitorData.data
        }
      }
    }
  },
  methods: {
    switchTab (tab) {
      this.activeTab = tab
    }
  }
}
</script>

<style lang="less">
#water-level-chart {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(6, 30, 93, 0.8), rgba(15, 45, 120, 0.6));
  border: 1px solid rgba(1, 153, 209, .3);
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 0 15px rgba(1, 153, 209, 0.2);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 18px;

    .card-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, #03d3ec, #1294fb);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      box-shadow: 0 2px 8px rgba(3, 211, 236, 0.3);

      i {
        font-size: 16px;
        color: #fff;
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 15px;
    overflow: hidden;

    .left-panel {
      width: 40%;
      display: flex;
      flex-direction: column;
      gap: 15px;

      .visitor-overview {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .overview-card {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          text-align: center;
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
          }

          .card-number {
            font-size: 22px;
            font-weight: bold;
            color: #03d3ec;
            margin-bottom: 4px;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
          }

          .card-label {
            font-size: 10px;
            color: #b8c5d1;
          }
        }
      }

      .visitor-distribution {
        .section-title {
          font-size: 14px;
          color: #ffffff;
          margin: 0 0 15px 0;
          font-weight: bold;
        }

        .distribution-list {
          .distribution-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .type-label {
              width: 70px;
              font-size: 12px;
              color: #b8c5d1;
              margin-right: 10px;
            }

            .progress-bar {
              flex: 1;
              height: 8px;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 4px;
              margin-right: 10px;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #03d3ec, #1294fb);
                border-radius: 4px;
                transition: width 0.3s ease;
              }
            }

            .percentage {
              width: 35px;
              font-size: 12px;
              color: #03d3ec;
              font-weight: bold;
              text-align: right;
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;

      .visitor-records {
        flex: 1;
        display: flex;
        flex-direction: column;

        .tabs {
          display: flex;
          margin-bottom: 15px;

          .tab {
            padding: 8px 16px;
            font-size: 12px;
            color: #b8c5d1;
            cursor: pointer;
            border-radius: 6px;
            margin-right: 10px;
            transition: all 0.3s ease;

            &.active {
              background: rgba(3, 211, 236, 0.2);
              color: #03d3ec;
              border: 1px solid rgba(3, 211, 236, 0.3);
            }

            &:hover:not(.active) {
              background: rgba(255, 255, 255, 0.1);
              color: #ffffff;
            }
          }
        }

        .visitor-table {
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }

  // 自定义滚动表格样式
  /deep/ .dv-scroll-board {
    .header {
      background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
      box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

      .header-item {
        color: #ffffff;
        font-weight: 600;
        font-size: 13px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }

    .rows {
      .row-item {
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
          background-color: rgba(25, 129, 246, 0.2) !important;
          transform: translateX(3px);
        }

        .ceil {
          color: #ffffff;
          font-size: 12px;

          // 姓名列
          &:nth-child(1) {
            color: #64b5f6;
            font-weight: 500;
          }

          // 时间列
          &:nth-child(2) {
            color: #ffb74d;
            font-weight: 500;
          }

          // 部门列
          &:nth-child(3) {
            color: #81c784;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
