<template>
  <div id="scroll-board">
    <!-- Tab 切换按钮 -->
    <div class="tab-container">
      <div class="tab-item" :class="{ active: activeTab === 'absence' }" @click="switchTab('absence')">
        未请假
      </div>
      <div class="tab-item" :class="{ active: activeTab === 'notReturned' }" @click="switchTab('notReturned')">
        未返校
      </div>
    </div>

    <!-- 滚动表格 -->
    <div class="board-content">
      <dv-scroll-board :config="config" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScrollBoard',
  data () {
    return {
      activeTab: 'absence', // 当前激活的tab，默认为未请假
      // 未请假数据
      absenceData: {
        header: ['学生姓名', '学院', '天数'],
        data: [
          ['xxx', '电子信息工程学院', '10'],
          ['xxx', '石油化工学院', '5'],
          ['xxx', '机械电气工程学院', '3'],
          ['xxx', '传媒学院', '2'],
          ['xxx', '生物工程学院', '1'],
          ['xxx', '电子信息工程学院', '1']
        ]
      },
      // 未返校数据
      notReturnedData: {
        header: ['序号', '学生姓名', '二级学院', '次数'],
        data: [
          ['xxx', '交通工程学院', '20'],
          ['xxx', '旅游与商务经济管理学院', '10'],
          ['xxx', '资源与环境学院', '5'],
          ['xxx', '交通工程学院', '4'],
          ['xxx', '旅游与商务经济管理学院', '3'],
          ['xxx', '资源与环境学院', '3'],
          ['xxx', '交通工程学院', '3'],
          ['xxx', '旅游与商务经济管理学院', '3']
        ]
      }
    }
  },
  computed: {
    config () {
      const baseConfig = {
        index: true,
        // columnWidth: [80, 120, 120, 150, 150],
        align: ['center'],
        rowNum: 7,
        headerBGC: '#1981f6',
        headerHeight: 45,
        oddRowBGC: 'rgba(0, 44, 81, 0.8)',
        evenRowBGC: 'rgba(10, 29, 50, 0.8)'
      }

      if (this.activeTab === 'absence') {
        return {
          ...baseConfig,
          header: this.absenceData.header,
          data: this.absenceData.data
        }
      } else {
        return {
          ...baseConfig,
          header: this.notReturnedData.header,
          data: this.notReturnedData.data
        }
      }
    }
  },
  methods: {
    switchTab (tab) {
      this.activeTab = tab
    }
  }
}
</script>

<style lang="less">
#scroll-board {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, .5);
  border-radius: 15px;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .tab-container {
    display: flex;
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.3);

    .tab-item {
      flex: 1;
      padding: 10px 20px;
      text-align: center;
      color: #ffffff;
      background-color: rgba(25, 129, 246, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        background-color: rgba(25, 129, 246, 0.5);
      }

      &.active {
        background-color: #1981f6;
        color: #ffffff;
        font-weight: 600;
      }

      &:not(:last-child) {
        border-right: 1px solid rgba(255, 255, 255, 0.1);
      }
    }
  }

  .board-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
