<template>
  <div id="college-table">
    <div class="table-header">
      <h3>二级学院数据统计</h3>
    </div>

    <!-- 自动循环滚动表格 -->
    <div class="table-content">
      <dv-scroll-board :config="config" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollegeTable',
  data () {
    return {
      // 学院数据
      collegeData: [
        ['电子信息工程学院', '100', '20', '80', '未按时回校人数', '1'],
        ['石油化工学院', '95', '18', '77', '未按时回校人数', '2'],
        ['机械电气工程学院', '88', '22', '66', '未按时回校人数', '3'],
        ['传媒学院', '92', '15', '77', '未按时回校人数', '4'],
        ['生物工程学院', '85', '25', '60', '未按时回校人数', '5'],
        ['交通工程学院', '90', '20', '70', '未按时回校人数', '6'],
        ['旅游与商务经济管理学院', '78', '12', '66', '未按时回校人数', '7'],
        ['资源与环境学院', '82', '18', '64', '未按时回校人数', '8'],
        ['纺织服装学院', '75', '15', '60', '未按时回校人数', '9'],
        ['新媒体产业学院', '70', '10', '60', '未按时回校人数', '10']
      ]
    }
  },
  computed: {
    config () {
      return {
        header: ['二级学院', '出门人数', '进门人数', '校外人数', '未按时回校人数', '排名'],
        data: this.collegeData,
        index: false, // 不显示序号，因为我们有排名列
        columnWidth: [200, 100, 100, 100, 140, 80], // 设置列宽
        align: ['left', 'center', 'center', 'center', 'center', 'center'], // 对齐方式
        rowNum: 6, // 显示行数
        headerBGC: '#1981f6', // 表头背景色
        headerHeight: 50, // 表头高度
        oddRowBGC: 'rgba(0, 44, 81, 0.8)', // 奇数行背景色
        evenRowBGC: 'rgba(10, 29, 50, 0.8)', // 偶数行背景色
        waitTime: 3000, // 滚动等待时间（毫秒）
        carousel: 'single', // 滚动方式：single（单行）或 page（整页）
        hoverPause: true // 鼠标悬停时暂停滚动
      }
    }
  },
  mounted () {
    // 组件挂载后开始数据更新循环
    this.startDataUpdate()
  },
  beforeDestroy () {
    // 组件销毁前清除定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  methods: {
    // 开始数据更新循环
    startDataUpdate () {
      this.updateTimer = setInterval(() => {
        this.updateCollegeData()
      }, 10000) // 每10秒更新一次数据
    },

    // 更新学院数据（模拟实时数据变化）
    updateCollegeData () {
      this.collegeData = this.collegeData.map(college => {
        const [name, outCount, inCount, , notReturnedDesc, rank] = college

        // 随机变化数据（模拟实时更新）
        const newOutCount = Math.max(0, parseInt(outCount) + Math.floor(Math.random() * 10 - 5))
        const newInCount = Math.max(0, parseInt(inCount) + Math.floor(Math.random() * 6 - 3))
        const newOutsideCount = Math.max(0, newOutCount - newInCount)

        return [
          name,
          newOutCount.toString(),
          newInCount.toString(),
          newOutsideCount.toString(),
          notReturnedDesc,
          rank
        ]
      })

      // 根据校外人数重新排序
      this.collegeData.sort((a, b) => parseInt(b[3]) - parseInt(a[3]))

      // 更新排名
      this.collegeData = this.collegeData.map((college, index) => {
        return [
          college[0], // 学院名称
          college[1], // 出门人数
          college[2], // 进门人数
          college[3], // 校外人数
          college[4], // 未按时回校人数描述
          (index + 1).toString() // 新排名
        ]
      })
    }
  }
}
</script>

<style lang="less">
#college-table {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: rgba(6, 30, 93, 0.5);
  border-top: 2px solid rgba(1, 153, 209, .5);
  border-radius: 15px;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .table-header {
    margin-bottom: 15px;
    text-align: center;

    h3 {
      color: #ffffff;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      text-shadow: 0 0 10px rgba(25, 129, 246, 0.8);
      letter-spacing: 2px;
    }
  }

  .table-content {
    flex: 1;
    overflow: hidden;

    // 自定义滚动表格样式
    /deep/ .dv-scroll-board {
      .header {
        background: linear-gradient(135deg, #1981f6 0%, #1565c0 100%);
        box-shadow: 0 2px 8px rgba(25, 129, 246, 0.3);

        .header-item {
          color: #ffffff;
          font-weight: 600;
          font-size: 14px;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
      }

      .rows {
        .row-item {
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:hover {
            background-color: rgba(25, 129, 246, 0.2) !important;
            transform: translateX(5px);
          }

          .ceil {
            color: #ffffff;
            font-size: 13px;

            // 学院名称列特殊样式
            &:first-child {
              color: #64b5f6;
              font-weight: 500;
            }

            // 数字列样式
            &:nth-child(2),
            &:nth-child(3),
            &:nth-child(4) {
              color: #81c784;
              font-weight: 500;
            }

            // 排名列样式
            &:last-child {
              color: #ffb74d;
              font-weight: 600;
              font-size: 14px;
            }
          }
        }

        // 排名前三的特殊样式
        .row-item:nth-child(1) .ceil:last-child {
          color: #ffd700; // 金色
          text-shadow: 0 0 5px rgba(255, 215, 0, 0.8);
        }

        .row-item:nth-child(2) .ceil:last-child {
          color: #c0c0c0; // 银色
          text-shadow: 0 0 5px rgba(192, 192, 192, 0.8);
        }

        .row-item:nth-child(3) .ceil:last-child {
          color: #cd7f32; // 铜色
          text-shadow: 0 0 5px rgba(205, 127, 50, 0.8);
        }
      }
    }
  }
}
</style>
