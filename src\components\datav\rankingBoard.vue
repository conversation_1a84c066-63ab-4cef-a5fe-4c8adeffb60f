<template>
  <div id="ranking-board">
    <div class="ranking-board-title">学院第三方人员情况</div>
    <div class="table-container">
      <table class="personnel-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>部门</th>
            <th>人数</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in personnelData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.department }}</td>
            <td>{{ item.count }}人</td>
            <td>
              <span class="status-indicator" :class="item.status === '正常' ? 'normal' : 'abnormal'">
                ● {{ item.status }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RankingBoard',
  data () {
    return {
      personnelData: [
        {
          department: '学工部',
          count: 8,
          status: '正常'
        },
        {
          department: '后勤处',
          count: 15,
          status: '正常'
        },
        {
          department: '保卫处',
          count: 12,
          status: '正常'
        },
        {
          department: '信息中心',
          count: 6,
          status: '异常'
        }
      ]
    }
  }
}
</script>

<style lang="less">
#ranking-board {
  width: 100%;
  height: 100%;
  box-shadow: 0 0 3px blue;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(6, 30, 93, 0.8), rgba(15, 45, 120, 0.6));
  border-radius: 15px;
  border: 1px solid rgba(1, 153, 209, .3);
  box-sizing: border-box;
  padding: 15px;
  color: #ffffff;

  .ranking-board-title {
    font-weight: bold;
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #ffffff;
    margin-bottom: 10px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  .personnel-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

    th {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      padding: 12px 8px;
      text-align: center;
      font-weight: 500;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    td {
      padding: 15px 8px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    tr:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .status-indicator {
      display: inline-flex;
      align-items: center;
      font-size: 12px;

      &.normal {
        color: #52c41a;
      }

      &.abnormal {
        color: #ff7875;
      }
    }
  }
}
</style>
